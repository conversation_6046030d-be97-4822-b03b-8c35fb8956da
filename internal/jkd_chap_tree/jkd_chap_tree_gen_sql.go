package jkd_chap_tree

import (
	"QuestionsDownloader/pkg/utils"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

var chineseToNum = map[string]int{
	"一":   1,
	"二":   2,
	"三":   3,
	"四":   4,
	"五":   5,
	"六":   6,
	"七":   7,
	"八":   8,
	"九":   9,
	"十":   10,
	"十一":  11,
	"十二":  12,
	"十三":  13,
	"十四":  14,
	"十五":  15,
	"十六":  16,
	"十七":  17,
	"十八":  18,
	"十九":  19,
	"二十":  20,
	"二十一": 21,
	"二十二": 22,
	"二十三": 23,
	"二十四": 24,
	"二十五": 25,
	"二十六": 26,
	"二十七": 27,
	"二十八": 28,
	"二十九": 29,
	"三十":  30,
	"三十一": 31,
	"三十二": 32,
	"三十三": 33,
	"三十四": 34,
	"三十五": 35,
	"三十六": 36,
	"三十七": 37,
	"三十八": 38,
	"三十九": 39,
	"四十":  40,
	"四十一": 41,
	"四十二": 42,
	"四十三": 43,
	"四十四": 44,
	"四十五": 45,
	"四十六": 46,
	"四十七": 47,
	"四十八": 48,
	"四十九": 49,
	"五十":  50,
}

var numToChinese = map[int]string{
	1:  "一",
	2:  "二",
	3:  "三",
	4:  "四",
	5:  "五",
	6:  "六",
	7:  "七",
	8:  "八",
	9:  "九",
	10: "十",
	11: "十一",
	12: "十二",
	13: "十三",
	14: "十四",
	15: "十五",
	16: "十六",
	17: "十七",
	18: "十八",
	19: "十九",
	20: "二十",
	21: "二十一",
	22: "二十二",
	23: "二十三",
	24: "二十四",
	25: "二十五",
	26: "二十六",
	27: "二十七",
	28: "二十八",
	29: "二十九",
	30: "三十",
	31: "三十一",
	32: "三十二",
	33: "三十三",
	34: "三十四",
	35: "三十五",
	36: "三十六",
	37: "三十七",
	38: "三十八",
	39: "三十九",
	40: "四十",
	41: "四十一",
	42: "四十二",
	43: "四十三",
	44: "四十四",
	45: "四十五",
	46: "四十六",
	47: "四十七",
	48: "四十八",
	49: "四十九",
	50: "五十",
}

var filterKeywords = []string{
	"试看",
	"综合练习",
	"教材变动",
	"强化练习",
	"案例精选",
	"案例备选",
	"附录",
	"案例分析",
	"案例练习",
}

type KnowledgeNode struct {
	CertificateCode string
	SubjectCode     string
	SubjectName     string
	ArticleName     string
	ChapterName     string
	SectionName     string
}

func ReadChapTreeCSVGenerateSQL(bathDir string) {
	// 读取文件夹下的所有文件
	dirEntries, err := os.ReadDir(bathDir)
	if err != nil {
		log.Printf("读取目录失败: %v", err)
		return
	}

	// 遍历文件，找出 csv 文件
	for _, entry := range dirEntries {
		if !entry.IsDir() && strings.HasSuffix(strings.ToLower(entry.Name()), ".csv") {
			// 打开文件
			file, err := os.Open(path.Join(bathDir, entry.Name()))
			if err != nil {
				log.Printf("打开文件失败: %v", err)
				continue
			}

			// 先执行处理
			if err := readCSVGenerateSQL(file); err != nil {
				log.Printf("处理文件 %s 失败: %v", entry.Name(), err)
			}

			// 最后关闭文件
			if err := file.Close(); err != nil {
				log.Printf("关闭文件 %s 失败: %v", entry.Name(), err)
			}
		}
	}

	log.Printf("SQL生成成功！")
	return
}

func readCSVGenerateSQL(file *os.File) error {
	data, err := readChapTreeCSV(file)
	if err != nil {
		return fmt.Errorf("读取 CSV 文件失败: %v", err)
	}

	// 生成 SQL
	sql, err := generateSQL(data)
	if err != nil {
		return fmt.Errorf("生成 SQL 失败: %v", err)
	}

	// 获取文件名，去掉路径和.csv后缀
	fileName := strings.TrimSuffix(filepath.Base(file.Name()), ".csv")

	// 写入 SQL 文件
	outputDir := "./chapTreeSQL"
	if err := utils.CreateDirectory(outputDir); err != nil {

	}
	err = os.WriteFile(filepath.Join(outputDir, fileName+".sql"), []byte(sql), 0644)
	if err != nil {
		return fmt.Errorf("写入 SQL 文件失败: %v", err)
	}

	return nil
}

func readChapTreeCSV(file *os.File) ([]KnowledgeNode, error) {
	// 创建 CSV reader
	reader := csv.NewReader(file)
	// 允许字段数量不统一
	reader.FieldsPerRecord = -1

	// 读取第一行（标题）
	_, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("读取 CSV 标题失败: %v", err)
	}

	var nodes []KnowledgeNode
	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("读取CSV内容失败: %v", err)
	}

	for _, record := range records {
		if len(record) < 4 { // 至少需要证书代码、科目代码、科目名称、章名称
			continue
		}

		node := KnowledgeNode{
			CertificateCode: strings.TrimSpace(record[0]),
			SubjectCode:     strings.TrimSpace(record[1]),
			SubjectName:     strings.TrimSpace(record[2]),
		}

		if len(record) == 4 { // 只有章结构
			node.ArticleName = ""
			node.ChapterName = strings.TrimSpace(record[3])
			node.SectionName = ""
		} else if len(record) == 5 { // 只有章节结构
			node.ArticleName = ""
			node.ChapterName = strings.TrimSpace(record[3])
			node.SectionName = strings.TrimSpace(record[4])
		} else if len(record) == 6 { // 有完整的篇章节结构
			node.ArticleName = strings.TrimSpace(record[3])
			node.ChapterName = strings.TrimSpace(record[4])
			node.SectionName = strings.TrimSpace(record[5])
		}

		// 只有在章名不为空的情况下才添加记录
		if node.ChapterName != "" {
			nodes = append(nodes, node)
		}
	}

	return nodes, nil
}

func generateSQL(data []KnowledgeNode) (string, error) {
	var articleSQL strings.Builder
	var chapterSQL strings.Builder
	var sectionSQL strings.Builder

	// 记录已处理的篇章节，避免重复
	processedArticles := make(map[string]bool)
	processedChapters := make(map[string]bool)

	articleCount := make(map[string]int) // 每个科目的篇计数
	chapterCount := make(map[string]int) // 每个科目的章计数
	sectionCount := make(map[string]int) // 每个章的节计数

	// Header
	articleSQL.WriteString("INSERT INTO t_article (subjectCode, articleCode, articleName, sortNum, createBy, updateBy) VALUES\n")
	chapterSQL.WriteString("INSERT INTO t_chapter (subjectCode, articleCode, chapterCode, chapterName, sortNum, createBy, updateBy) VALUES\n")
	sectionSQL.WriteString("INSERT INTO t_section (chapterCode, sectionCode, sectionName, sortNum, createBy, updateBy) VALUES\n")

	for _, row := range data {
		updateBy := "superAdmin"
		subjectCode := row.SubjectCode
		articleName := FormatKnowledgeName(row.ArticleName)
		chapterName := FormatKnowledgeName(row.ChapterName)
		sectionName := FormatKnowledgeName(row.SectionName)

		// 提取数字用于排序
		articleSortNum := ExtractSortNum(articleName)
		chapterSortNum := ExtractSortNum(chapterName)
		sectionSortNum := ExtractSortNum(sectionName)

		// 处理篇
		if articleName != "" && !processedArticles[subjectCode+articleName] {
			articleCode := funcGetArticleCode(subjectCode, articleSortNum)

			articleCount[subjectCode]++
			if articleSortNum != articleCount[subjectCode] {
				log.Printf("科目篇序号不对 %s %s %d %d",
					subjectCode, articleName, articleSortNum, articleCount[subjectCode])
			}

			articleSQL.WriteString(fmt.Sprintf("('%s', '%s', '%s', %d, '%s', '%s'),\n",
				subjectCode,
				articleCode,
				articleName,
				articleSortNum,
				updateBy,
				updateBy))
			processedArticles[subjectCode+articleName] = true
		}

		// 处理章
		if chapterName != "" && !processedChapters[subjectCode+chapterName] {
			articleCode := ""
			if articleName != "" {
				articleCode = funcGetArticleCode(subjectCode, articleSortNum)
			}
			chapterCode := funcGetChapterCode(subjectCode, chapterSortNum)

			// 处理不规范章名称
			if shouldFilterKnowledge(chapterName) {
				//log.Printf("章名称不符合规范，需要过滤掉 %s", chapterName)
			} else {
				chapterCount[subjectCode]++
			}

			if chapterSortNum != chapterCount[subjectCode] {
				log.Printf("科目章序号不对 %s %s %d %d",
					subjectCode, chapterName, chapterSortNum, chapterCount[subjectCode])
			}

			chapterSQL.WriteString(fmt.Sprintf("('%s', '%s', '%s', '%s', %d, '%s', '%s'),\n",
				subjectCode,
				articleCode,
				chapterCode,
				chapterName,
				chapterSortNum,
				updateBy,
				updateBy))
			processedChapters[subjectCode+chapterName] = true
		}

		// 处理节
		if sectionName != "" {
			chapterCode := funcGetChapterCode(subjectCode, chapterSortNum)
			sectionCode := funcGetSectionCode(chapterCode, sectionSortNum)

			// 处理不规范节名称
			if isChapterName(sectionName) {
				//log.Printf("节名称不符合规范，是对应章名称，需要过滤掉 %s", sectionName)
				continue
			}
			if shouldFilterKnowledge(sectionName) {
				//log.Printf("节名称不符合规范，需要过滤掉 %s", sectionName)
			} else {
				sectionCount[chapterCode]++
			}
			if sectionSortNum != sectionCount[chapterCode] {
				log.Printf("科目节序号不对 %s %s %d %d",
					subjectCode, sectionName, sectionSortNum, sectionCount[chapterCode])
				continue
			}

			sectionSQL.WriteString(fmt.Sprintf("('%s', '%s', '%s', %d, '%s', '%s'),\n",
				chapterCode,
				sectionCode,
				sectionName,
				sectionSortNum,
				updateBy,
				updateBy))
		}
	}

	// 合并并处理最后的逗号
	sql := strings.TrimSuffix(articleSQL.String(), ",\n") + ";\n\n" +
		strings.TrimSuffix(chapterSQL.String(), ",\n") + ";\n\n" +
		strings.TrimSuffix(sectionSQL.String(), ",\n") + ";"

	return sql, nil
}

// 检查篇、章、节名称是否需要被过滤
func shouldFilterKnowledge(name string) bool {
	for _, keyword := range filterKeywords {
		if strings.Contains(name, keyword) {
			return true
		}
	}
	return false
}

func isChapterName(sectionName string) bool {
	re := regexp.MustCompile(`^(第)?(\d+|[一二三四五六七八九十]+)(章)(.+)$`)
	matches := re.FindStringSubmatch(sectionName)
	return len(matches) == 5
}

func funcGetArticleCode(subjectCode string, sortNum int) string {
	articleCode := fmt.Sprintf("%s-ART%02d", subjectCode, sortNum)
	return articleCode
}

func funcGetChapterCode(subjectCode string, sortNum int) string {
	chapterCode := fmt.Sprintf("%s-CHAP%02d", subjectCode, sortNum)
	return chapterCode
}

func funcGetSectionCode(chapterCode string, sortNum int) string {
	sectionCode := fmt.Sprintf("%s-SEC%02d", chapterCode, sortNum)
	return sectionCode
}

func FormatKnowledgeName(text string) string {
	// 判断空或空字符串
	if text == "" || strings.TrimSpace(text) == "" {
		return text
	}

	prefix := funcGetNamePrefix(text)
	text = strings.TrimPrefix(text, prefix)

	// 去除所有空格、*
	text = strings.ReplaceAll(text, " ", "")
	text = strings.ReplaceAll(text, "*", "")
	text = strings.ReplaceAll(text, "　", "") // 全角空格

	// 先匹配小数格式
	re := regexp.MustCompile(`^(\d+\.\d+)(.+)$`)
	matches := re.FindStringSubmatch(text)
	if len(matches) == 3 {
		return fmt.Sprintf("%s%s %s", prefix, matches[1], strings.TrimSpace(matches[2]))
	}

	// 匹配格式: (第)数字+类型+名称
	re = regexp.MustCompile(`^(第)?(\d+|[一二三四五六七八九十]+)(篇|章|节)(.+)$`)
	matches = re.FindStringSubmatch(text)
	if len(matches) != 5 {
		return prefix + text // 如果不匹配格式，也要加上前缀
	}

	var chineseNum string
	if num, err := strconv.Atoi(matches[2]); err == nil {
		// 如果是阿拉伯数字，转成中文
		chineseNum = numToChinese[num]
	} else {
		// 如果是中文数字，直接使用
		chineseNum = matches[2]
	}
	if chineseNum == "" {
		return prefix + text // 如果转换失败，也要加上前缀
	}

	// 组合成最终格式：[前缀]第X篇/章/节 名称
	return fmt.Sprintf("%s第%s%s %s", prefix, chineseNum, matches[3], strings.TrimSpace(matches[4]))
}

func funcGetNamePrefix(text string) string {
	// 定义需要保留的前缀，包含中英文方括号
	prefixes := []string{
		"[试看]", "【试看】",
		"[预览]", "【预览】",
		"[免费]", "【免费】",
		"[推荐]", "【推荐】",
		"[新]", "【新】",
		"[热门]", "【热门】",
	}

	// 查找并保存前缀
	var prefix string
	for _, p := range prefixes {
		if strings.HasPrefix(text, p) {
			prefix = p
			break
		}
	}
	return prefix
}

func ExtractSortNum(text string) int {
	if text == "" || strings.TrimSpace(text) == "" {
		return 0
	}
	// 如果有前缀的，默认排序号为 0
	if funcGetNamePrefix(text) != "" {
		return 0
	}

	number := ExtractNumber(text)
	// 处理类似 "12.34 施工成本管理内容与方法" 这种格式，返回小数部分
	re := regexp.MustCompile(`^(\d+)\.(\d+)`)
	if matches := re.FindStringSubmatch(number); len(matches) > 2 {
		// matches[1] 是整数部分 "12"
		// matches[2] 是小数部分 "34"
		num, _ := strconv.Atoi(matches[2]) // 取小数部分
		return num
	}

	if num, err := strconv.Atoi(number); err == nil {
		return num
	} else {
		log.Printf("转换数字 %s 失败: %v", number, err)
		return 0
	}
}

func ExtractNumber(text string) string {
	if text == "" || strings.TrimSpace(text) == "" {
		return text
	}

	// 1. 处理类似 "第X篇" 或 "第X章" 的格式
	re := regexp.MustCompile(`第[一二三四五六七八九十\d]+[篇章节]`)
	if match := re.FindString(text); match != "" {
		// 提取数字部分
		numStr := strings.TrimPrefix(match, "第")
		numStr = strings.TrimRight(numStr, "篇章节")

		// 尝试解析阿拉伯数字
		if num, err := strconv.Atoi(numStr); err == nil {
			return strconv.Itoa(num)
		}

		// 解析中文数字
		if num, ok := chineseToNum[numStr]; ok {
			return strconv.Itoa(num)
		}
	}

	// 2. 处理类似 "12.2 施工成本管理内容与方法" 这种格式
	re = regexp.MustCompile(`^(\d+\.\d+)`)
	if matches := re.FindStringSubmatch(text); len(matches) > 0 {
		return matches[1]
	}

	// 3. 处理其他包含数字的情况
	re = regexp.MustCompile(`\d+`)
	if matches := re.FindStringSubmatch(text); len(matches) > 0 {
		num, _ := strconv.Atoi(matches[0])
		return strconv.Itoa(num)
	}

	return "0"
}
