package jkd_dwld

import (
	"QuestionsDownloader/pkg/utils"
	"QuestionsDownloader/types"
	"fmt"
	"log"
	"path"
	"sync"
)

func ProcessSubjects(
	downloadDir string,
	subjectConfigs []types.SubjectConfig,
) []types.SubjectConfig {
	log.Printf("=== 开始执行 ProcessSubjects ===")
	log.Printf("参数 downloadDir: '%s'", downloadDir)
	log.Printf("参数 subjectConfigs 数量: %d", len(subjectConfigs))

	var exportSubjects []types.SubjectConfig
	var exportMutex sync.Mutex

	// 处理函数
	processFunc := func(downloadDir string, subject types.SubjectConfig) error {
		log.Printf("→ 开始处理科目: %s (ID: %s)", subject.JKDSubjectName, subject.JKDSubjectId)
		export, err := processSubject(downloadDir, subject)
		if err != nil {
			log.Printf("✗ 处理科目失败: %s, 错误: %v", subject.JKDSubjectName, err)
			return err
		}

		exportMutex.Lock()
		exportSubjects = append(exportSubjects, export)
		exportMutex.Unlock()
		log.Printf("✓ 科目处理成功: %s", subject.JKDSubjectName)
		return nil
	}

	// 读取处理模式
	log.Println("步骤1: 读取处理模式")
	mode := utils.MustGetEnv("JKD_DOWNLOAD_PROCESS_MODE")
	log.Printf("✓ 处理模式: '%s'", mode)

	var err error
	switch mode {
	case "serial":
		log.Println("步骤2: 使用串行模式处理科目")
		err = processSerially(downloadDir, subjectConfigs, processFunc)
	case "concurrent":
		log.Println("步骤2: 使用并发模式处理科目")
		err = processConcurrently(downloadDir, subjectConfigs, processFunc)
	default:
		log.Printf("✗ 未知的执行模式: %s", mode)
		return nil
	}

	if err != nil {
		log.Printf("✗ 处理下载所有科目出现错误: %v", err)
		return nil
	}

	log.Printf("✓ 所有科目处理完成，成功处理 %d 个科目", len(exportSubjects))
	log.Println("=== ProcessSubjects 执行完成 ===")
	return exportSubjects
}

// processes subjects in serial mode
func processSerially(
	downloadDir string,
	subjectConfigs []types.SubjectConfig,
	processFunc func(downloadDir string, subject types.SubjectConfig) error,
) error {
	log.Println("使用串行模式处理下载任务...")

	for _, subject := range subjectConfigs {
		if err := processFunc(downloadDir, subject); err != nil {
			return fmt.Errorf("串行处理科目 %s 时出错: %v", subject.JKDSubjectName, err)
		}
	}

	return nil
}

// processes subjects in concurrent mode
func processConcurrently(
	downloadDir string,
	subjectConfigs []types.SubjectConfig,
	processFunc func(downloadDir string, subject types.SubjectConfig) error,
) error {
	maxConcurrent := utils.MustGetEnvAsInt("JKD_DOWNLOAD_PROCESS_MAX_CONCURRENT")
	log.Printf("使用并发模式处理下载任务（最大并发数：%d）...\n", maxConcurrent)

	// 创建信号量来控制并发数
	semaphore := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup
	errors := make(chan error, len(subjectConfigs))

	for _, subject := range subjectConfigs {
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量

		go func(subject types.SubjectConfig) {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			if err := processFunc(downloadDir, subject); err != nil {
				errors <- fmt.Errorf("并发处理科目 %s 时出错: %v", subject.JKDSubjectName, err)
			}
		}(subject)
	}

	// 等待所有任务完成
	wg.Wait()
	close(errors)

	// 检查是否有错误发生
	for err := range errors {
		if err != nil {
			return err
		}
	}

	return nil
}

// processes a single subject download and returns export info
func processSubject(
	downloadDir string,
	subject types.SubjectConfig,
) (types.SubjectConfig, error) {
	log.Printf("  === 开始处理单个科目 ===")
	log.Printf("  科目名称: '%s'", subject.JKDSubjectName)
	log.Printf("  科目ID: '%s'", subject.JKDSubjectId)
	log.Printf("  证书名称: '%s'", subject.JKDCertificateName)
	log.Printf("  子目录: '%s'", subject.SubDir)

	// 获取更新名称
	log.Printf("  步骤1: 获取认证信息")
	userId := utils.MustGetEnv("JKD_AUTH_USER_ID")
	password := utils.MustGetEnv("JKD_AUTH_PASSWORD")
	log.Printf("  用户ID: '%s'", userId)
	log.Printf("  密码: '%s'", password)

	log.Printf("  步骤2: 调用 GetSubjectUpdateName API")
	updateName, err := GetSubjectUpdateName(subject.JKDSubjectId, userId, password)
	if err != nil {
		log.Printf("  ✗ 获取更新名称失败: %v", err)
		return types.SubjectConfig{}, fmt.Errorf("获取更新名称失败: %v", err)
	}
	log.Printf("  ✓ 获取更新名称成功: '%s'", updateName)

	// 创建下载目录
	downloadPath := path.Join(downloadDir, subject.SubDir)
	log.Printf("  步骤3: 创建下载目录 '%s'", downloadPath)
	if err := utils.CreateDirectory(downloadPath); err != nil {
		log.Printf("  ✗ 创建下载目录失败: %v", err)
		return types.SubjectConfig{}, fmt.Errorf("创建下载目录失败: %v", err)
	}
	log.Printf("  ✓ 下载目录创建成功")

	// 下载文件
	log.Printf("  步骤4: 下载文件 '%s' 到 '%s'", updateName, downloadPath)
	if err := DownloadSubjectFile(updateName, downloadPath); err != nil {
		log.Printf("  ✗ 下载文件失败: %v", err)
		return types.SubjectConfig{}, fmt.Errorf("下载文件失败: %v", err)
	}
	log.Printf("  ✓ 文件下载成功")

	// 返回导出信息
	log.Printf("  步骤5: 构建导出配置")
	exportConfig := types.SubjectConfig{
		JKDCertificateId:   subject.JKDCertificateId,
		JKDCertificateName: subject.JKDCertificateName,
		JKDSubjectId:       subject.JKDSubjectId,
		JKDSubjectName:     subject.JKDSubjectName,
		JKDUpdateName:      updateName,
		SubDir:             subject.SubDir,
		LexCertificate:     subject.LexCertificate,
		LexSubject:         subject.LexSubject,
		CertificateCode:    subject.CertificateCode,
		SubjectCode:        subject.SubjectCode,
	}
	log.Printf("  ✓ 导出配置构建完成")
	log.Printf("  === 单个科目处理完成 ===")

	return exportConfig, nil
}
