package jkd_dwld

import (
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// SOAPEnvelope represents the XML structure for SOAP responses
type SOAPEnvelope struct {
	XMLName xml.Name `xml:"Envelope"`
	Body    struct {
		GetCourseUpdateNameResponse struct {
			Result string `xml:"GetCourseUpdateNameResult"`
		} `xml:"GetCourseUpdateNameResponse"`
	} `xml:"Body"`
}

const (
	soapURL          = "http://iphone.jinkaodian.com/ExamJsonService.asmx"
	downloadURL      = "http://www.jinkaodian.com/CL.ExamWebService/subject/%s.zip"
	soapBodyTemplate = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Header>
        <MGSoapHeader xmlns="http://tempuri.org/">
            <UserID>%s</UserID>
            <Password>%s</Password>
        </MGSoapHeader>
    </soap:Header>
    <soap:Body>
        <GetCourseUpdateName xmlns="http://tempuri.org/">
            <courseId>%s</courseId>
            <district></district>
            <courseType>1</courseType>
        </GetCourseUpdateName>
    </soap:Body>
</soap:Envelope>`
)

// GetSubjectUpdateName retrieves update name for a subject via SOAP request
func GetSubjectUpdateName(subjectId string, userId string, password string) (string, error) {
	log.Printf("    === 开始 SOAP API 调用 ===")
	log.Printf("    请求参数 - subjectId: '%s', userId: '%s'", subjectId, userId)

	// 准备 SOAP 请求体
	log.Printf("    步骤1: 构建 SOAP 请求体")
	body := fmt.Sprintf(soapBodyTemplate, userId, password, subjectId)
	log.Printf("    请求体长度: %d 字节", len(body))

	log.Printf("    步骤2: 创建 HTTP 请求")
	req, err := http.NewRequest("POST", soapURL, strings.NewReader(body))
	if err != nil {
		log.Printf("    ✗ 创建 HTTP 请求失败: %v", err)
		return "", err
	}
	log.Printf("    ✓ HTTP 请求创建成功，URL: %s", soapURL)

	// 设置请求头
	log.Printf("    步骤3: 设置请求头")
	req.Header.Set("Host", "iphone.jinkaodian.com")
	req.Header.Set("SOAPAction", "http://tempuri.org/GetCourseUpdateName")
	req.Header.Set("Content-Type", "text/xml; charset=utf-8")
	req.Header.Set("User-Agent", "éèå¸ 3.15 (iPad; iPadOS 17.5; en_CN)")
	log.Printf("    ✓ 请求头设置完成")

	// 发送请求
	log.Printf("    步骤4: 发送 SOAP 请求")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("    ✗ 发送请求失败: %v", err)
		return "", err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Printf("    警告: 关闭响应体失败: %v", err)
		}
	}(resp.Body)
	log.Printf("    ✓ 请求发送成功，状态码: %d", resp.StatusCode)

	// 读取响应
	log.Printf("    步骤5: 读取响应内容")
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("    ✗ 读取响应失败: %v", err)
		return "", err
	}
	log.Printf("    ✓ 响应读取成功，长度: %d 字节", len(respBody))

	// 解析 XML 响应
	log.Printf("    步骤6: 解析 XML 响应")
	var envelope SOAPEnvelope
	if err := xml.Unmarshal(respBody, &envelope); err != nil {
		log.Printf("    ✗ XML 解析失败: %v", err)
		log.Printf("    响应内容: %s", string(respBody))
		return "", err
	}

	result := envelope.Body.GetCourseUpdateNameResponse.Result
	log.Printf("    ✓ XML 解析成功，获取到更新名称: '%s'", result)
	log.Printf("    === SOAP API 调用完成 ===")

	return result, nil
}

// DownloadSubjectFile 下载指定文件到目标路径
func DownloadSubjectFile(updateName, downloadPath string) error {
	log.Printf("    === 开始文件下载 ===")
	log.Printf("    更新名称: '%s'", updateName)
	log.Printf("    下载路径: '%s'", downloadPath)

	url := fmt.Sprintf(downloadURL, updateName)
	log.Printf("    步骤1: 构建下载 URL: '%s'", url)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("    ✗ 创建下载请求失败: %v", err)
		return err
	}
	log.Printf("    ✓ 下载请求创建成功")

	// 设置请求头
	log.Printf("    步骤2: 设置请求头")
	req.Header.Set("Host", "www.jinkaodian.com")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("User-Agent", "jkdexam/3.15 CFNetwork/1496.0.7 Darwin/23.5.0")
	req.Header.Set("Accept-Language", "zh-CN,zh-Hans;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	log.Printf("    ✓ 请求头设置完成")

	// 发送请求
	log.Printf("    步骤3: 发送下载请求")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("    ✗ 发送下载请求失败: %v", err)
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Printf("    警告: 关闭响应体失败: %v", err)
		}
	}(resp.Body)
	log.Printf("    ✓ 下载请求发送成功，状态码: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("    ✗ 下载失败，HTTP 状态码: %d", resp.StatusCode)
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建文件
	filename := filepath.Join(downloadPath, updateName+".zip")
	log.Printf("    步骤4: 创建本地文件 '%s'", filename)
	file, err := os.Create(filename)
	if err != nil {
		log.Printf("    ✗ 创建本地文件失败: %v", err)
		return err
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			log.Printf("    警告: 关闭文件失败: %v", err)
		}
	}(file)
	log.Printf("    ✓ 本地文件创建成功")

	// 写入文件
	log.Printf("    步骤5: 开始写入文件内容")
	bytesWritten, err := io.Copy(file, resp.Body)
	if err != nil {
		log.Printf("    ✗ 写入文件失败: %v", err)
		return err
	}
	log.Printf("    ✓ 文件写入成功，共写入 %d 字节", bytesWritten)
	log.Printf("    === 文件下载完成 ===")

	return nil
}
