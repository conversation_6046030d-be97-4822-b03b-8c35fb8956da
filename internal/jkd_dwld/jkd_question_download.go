package jkd_dwld

import (
	"QuestionsDownloader/pkg/utils"
	"QuestionsDownloader/types"
	"fmt"
	"log"
)

const (
	exportSubjectConfigsWithUpdateNameFilename = "subject_configs_with_update_name.csv"
)

func QuestionDownloadAndUpZip(downloadDir string, subjectConfigs []types.SubjectConfig) {
	log.Printf("=== 开始执行 QuestionDownloadAndUpZip ===")
	log.Printf("参数 downloadDir: '%s'", downloadDir)
	log.Printf("参数 subjectConfigs 数量: %d", len(subjectConfigs))

	// 下载所有科目题目
	log.Println("步骤1: 开始下载所有科目题目")
	QuestionDownload(downloadDir, subjectConfigs)
	log.Println("✓ 下载所有科目题目完成")

	// 解压所有下载的文件
	log.Println("步骤2: 开始解压所有下载的文件")
	utils.UnzipFiles(downloadDir)
	log.Println("✓ 解压所有下载的文件完成")

	// 删除所有压缩文件
	log.Println("步骤3: 开始删除所有压缩文件")
	utils.RemoveZipFiles(downloadDir)
	log.Println("✓ 删除所有压缩文件完成")

	log.Println("=== QuestionDownloadAndUpZip 执行完成 ===")
	fmt.Println("所有科目题目已下载，并且解压完成...")
}

func QuestionDownload(downloadDir string, subjectConfigs []types.SubjectConfig) {
	log.Printf("=== 开始执行 QuestionDownload ===")
	log.Printf("参数 downloadDir: '%s'", downloadDir)
	log.Printf("参数 subjectConfigs 数量: %d", len(subjectConfigs))

	// 创建下载根目录（如果存在，则删除后再创建）
	log.Printf("步骤1: 创建/重建下载目录 '%s'", downloadDir)
	if err := utils.DelAndCreateDirectory(downloadDir); err != nil {
		log.Printf("✗ 创建下载目录失败: %v", err)
		return
	}
	log.Printf("✓ 下载目录创建成功")

	// 处理所有科目
	log.Printf("步骤2: 开始处理 %d 个科目", len(subjectConfigs))
	exportSubjects := ProcessSubjects(downloadDir, subjectConfigs)
	if exportSubjects == nil {
		log.Printf("✗ ProcessSubjects 返回 nil，处理失败")
		return
	}
	if len(exportSubjects) == 0 {
		log.Printf("✗ ProcessSubjects 返回空数组，没有成功处理的科目")
		return
	}
	log.Printf("✓ 成功处理 %d 个科目", len(exportSubjects))

	log.Println("步骤3: 导出科目配置到 CSV 文件")
	log.Printf("  导出文件名: '%s'", exportSubjectConfigsWithUpdateNameFilename)
	utils.ExportSubjectConfigsToCSV(exportSubjects, exportSubjectConfigsWithUpdateNameFilename)
	log.Printf("✓ CSV 文件导出完成")

	log.Println("=== QuestionDownload 执行完成 ===")
	fmt.Println("所有科目题目已下载完成...")
}
