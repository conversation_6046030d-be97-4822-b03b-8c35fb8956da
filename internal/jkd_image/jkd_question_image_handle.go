package jkd_image

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

const (
	sourceSubDir      = "questionimages"
	destinationSubDir = "questionimagesWithJKD"
	tempSubDir        = "tempOcr"
	maxWorkers        = 50 // 限制并发数量
)

// 定义要搜索的关键词
var keywords = []string{"金考典", "jkd", "jinkaodian"}

// 支持的图片格式
var validExtensions = map[string]bool{
	".jpg":  true,
	".jpeg": true,
	".png":  true,
	".bmp":  true,
	".tiff": true,
}

// Result 结果结构
type Result struct {
	FilePath string
	Keywords []string
}

// ImageTask 图片处理任务结构
type ImageTask struct {
	FilePath string
	TempDir  string
}

// ProcessStats 处理统计
type ProcessStats struct {
	TotalFiles     int
	ProcessedFiles int
	MatchedFiles   int
	StartTime      time.Time
	mu             sync.Mutex
}

func (ps *ProcessStats) increment(matched bool) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.ProcessedFiles++
	if matched {
		ps.MatchedFiles++
	}

	// 打印进度
	elapsed := time.Since(ps.StartTime)
	progress := float64(ps.ProcessedFiles) / float64(ps.TotalFiles) * 100
	fmt.Printf("\r进度: %.2f%% (%d/%d) 匹配: %d 耗时: %v",
		progress, ps.ProcessedFiles, ps.TotalFiles, ps.MatchedFiles, elapsed.Round(time.Second))
}

func QuestionImageHandle(baseDir string) {
	startTime := time.Now()
	fmt.Printf("开始处理图片...\n基础目录: %s\n", baseDir)

	// 创建临时目录
	tempDir := path.Join(baseDir, tempSubDir)
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		fmt.Printf("创建临时目录错误: %v\n", err)
		return
	}
	defer os.RemoveAll(tempDir)

	// 创建目标目录
	destinationDir := path.Join(baseDir, destinationSubDir)
	if err := os.MkdirAll(destinationDir, 0755); err != nil {
		fmt.Printf("创建目标目录错误: %v\n", err)
		return
	}

	// 检查是否安装了tesseract
	if !checkTesseract() {
		fmt.Println("请先安装Tesseract OCR工具")
		fmt.Println("Ubuntu/Debian: sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim")
		fmt.Println("MacOS: brew install tesseract tesseract-lang")
		return
	}

	sourceDir := path.Join(baseDir, sourceSubDir)

	// 首先统计文件总数
	stats := &ProcessStats{
		StartTime: startTime,
	}

	err := filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			if validExtensions[ext] {
				stats.TotalFiles++
			}
		}
		return nil
	})

	if err != nil {
		fmt.Printf("统计文件数量错误: %v\n", err)
		return
	}

	fmt.Printf("找到 %d 个图片文件\n", stats.TotalFiles)
	if stats.TotalFiles == 0 {
		fmt.Println("没有找到需要处理的图片文件")
		return
	}

	// 创建工作池
	tasks := make(chan ImageTask, maxWorkers)
	results := make(chan Result, maxWorkers)
	var wg sync.WaitGroup

	// 启动固定数量的worker
	workerCount := runtime.NumCPU() * 2
	if workerCount > maxWorkers {
		workerCount = maxWorkers
	}

	fmt.Printf("启动 %d 个工作协程\n", workerCount)

	// 启动workers
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go worker(tasks, &wg, results, stats)
	}

	// 发送任务
	go func() {
		err := filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if !info.IsDir() {
				ext := strings.ToLower(filepath.Ext(path))
				if validExtensions[ext] {
					tasks <- ImageTask{
						FilePath: path,
						TempDir:  tempDir,
					}
				}
			}
			return nil
		})

		if err != nil {
			fmt.Printf("\n遍历目录错误: %v\n", err)
		}
		close(tasks)
	}()

	// 等待所有任务完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 处理结果
	for result := range results {
		if len(result.Keywords) > 0 {
			relPath, err := filepath.Rel(sourceDir, result.FilePath)
			if err != nil {
				fmt.Printf("\n计算相对路径错误: %v\n", err)
				continue
			}
			destPath := filepath.Join(destinationDir, relPath)

			if err := copyFile(result.FilePath, destPath); err != nil {
				fmt.Printf("\n复制文件错误: %v\n", err)
			}
		}
	}

	// 打印最终统计
	elapsed := time.Since(startTime)
	fmt.Printf("\n\n处理完成:\n")
	fmt.Printf("总文件数: %d\n", stats.TotalFiles)
	fmt.Printf("处理文件数: %d\n", stats.ProcessedFiles)
	fmt.Printf("匹配文件数: %d\n", stats.MatchedFiles)
	fmt.Printf("总耗时: %v\n", elapsed.Round(time.Second))
	fmt.Printf("平均处理速度: %.2f 文件/秒\n", float64(stats.ProcessedFiles)/elapsed.Seconds())
}

func worker(tasks <-chan ImageTask, wg *sync.WaitGroup, results chan<- Result, stats *ProcessStats) {
	defer wg.Done()

	for task := range tasks {
		// 为当前图片创建临时输出文件
		tempOutputBase := filepath.Join(task.TempDir, filepath.Base(task.FilePath))

		// 运行tesseract命令
		cmd := exec.Command("tesseract", task.FilePath, tempOutputBase, "-l", "chi_sim+eng")
		if err := cmd.Run(); err != nil {
			fmt.Printf("\nOCR处理错误 %s: %v\n", task.FilePath, err)
			stats.increment(false)
			continue
		}

		// 读取OCR结果
		textBytes, err := os.ReadFile(tempOutputBase + ".txt")
		if err != nil {
			fmt.Printf("\n读取OCR结果错误 %s: %v\n", task.FilePath, err)
			stats.increment(false)
			continue
		}

		// 清理临时文件
		os.Remove(tempOutputBase + ".txt")

		// 检查关键词
		text := strings.ToLower(string(textBytes))
		var foundKeywords []string
		for _, keyword := range keywords {
			if strings.Contains(text, strings.ToLower(keyword)) {
				foundKeywords = append(foundKeywords, keyword)
			}
		}

		// 更新统计并发送结果
		hasMatch := len(foundKeywords) > 0
		stats.increment(hasMatch)

		if hasMatch {
			results <- Result{
				FilePath: task.FilePath,
				Keywords: foundKeywords,
			}
		}
	}
}

// 检查是否安装了tesseract
func checkTesseract() bool {
	cmd := exec.Command("tesseract", "--version")
	return cmd.Run() == nil
}

// 复制文件
func copyFile(src, dst string) error {
	// 创建目标目录
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return fmt.Errorf("创建目录错误: %v", err)
	}

	source, err := os.Open(src)
	if err != nil {
		return err
	}
	defer source.Close()

	destination, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destination.Close()

	_, err = io.Copy(destination, source)
	return err
}
