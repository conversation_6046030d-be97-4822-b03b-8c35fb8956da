# JKD 题库下载器 (JKD Questions Downloader)

## 项目概括

本项目是一个专业的金考典(JKD)题库数据处理系统，基于 Go 语言开发。系统提供完整的题库数据处理流水线，包括题目下载、数据导入、图片处理、章节结构生成等核心功能。通过环境变量 `PROCESS_TYPE` 控制不同的执行模式，实现模块化的数据处理流程。

## 技术选型

- **主要编程语言**: Go 1.23.2+
- **关键库/框架**: 
  - `github.com/joho/godotenv` - 环境变量管理
  - `net/http` - HTTP客户端和服务器
  - `mime/multipart` - 文件上传处理
  - `encoding/csv` - CSV文件处理
  - `regexp` - 正则表达式处理
- **数据存储**: CSV配置文件、Excel文件、SQL文件
- **版本控制**: Git
- **其他工具**: Tesseract OCR (图片文字识别)、Python脚本 (辅助处理)

## 项目结构

```
jkd-QuestionsDownloader/
├── cmd/
│   └── main.go                    # 程序入口点
├── internal/                      # 核心业务模块
│   ├── jkd_dwld/                 # 题目下载模块
│   │   ├── jkd_question_download.go
│   │   ├── process.go
│   │   └── soap.go
│   ├── jkd_import/               # 题库导入模块
│   │   └── jkd_question_set_import.go
│   ├── jkd_image/                # 图片处理模块
│   │   └── jkd_question_image_handle.go
│   └── jkd_chap_tree/            # 章节结构生成模块
│       └── jkd_chap_tree_gen_sql.go
├── pkg/utils/                     # 工具函数库
│   ├── create_dir.go
│   ├── env_utils.go
│   ├── read_csv.go
│   └── unzip.go
├── types/
│   └── subject_types.go           # 数据类型定义
├── jkd-script/                    # 辅助Python脚本
├── chapTreeSQL/                   # 生成的SQL文件目录
├── subject_configs_*.csv          # 科目配置文件
├── .env                          # 系统配置文件
├── .env.jkd                      # JKD专用配置
└── go.mod                        # Go模块依赖
```

## 核心执行流程详解

### 1. 程序入口与配置加载 (`cmd/main.go`)

#### 1.1 初始化流程
```go
func init() {
    if err := loadEnvFiles(); err != nil {
        log.Fatalf("配置文件加载失败: %v", err)
    }
}
```

**必需配置文件**:
- `.env` - 系统配置文件
- `.env.jkd` - JKD专用配置

**配置加载顺序**:
1. 加载 `.env` 文件
2. 加载 `.env.jkd` 文件
3. 验证必需配置项

#### 1.2 主程序流程
```go
func main() {
    // 1. 读取环境变量配置
    processType := utils.MustGetEnv("PROCESS_TYPE")
    subjectConfigFilename := utils.MustGetEnv("SUBJECT_CONFIG_FILENAME")
    exportDataBathPath := utils.MustGetEnv("EXPORT_DATA_BATH_PATH")
    
    // 2. 读取科目配置文件
    subjectConfigsFromCSV := utils.ReadSubjectConfigsCSV(subjectConfigFilename)
    
    // 3. 构建处理目录路径
    dirPath := path.Join(exportDataBathPath, "export_data")
    
    // 4. 根据PROCESS_TYPE执行对应流程
    switch processType {
        case "TEST": ...
        case "DOWNLOAD_JKD": ...
        case "IMPORT_MGT": ...
        case "QUESTION_IMAGES_HANDLE": ...
        case "GENERATE_CHAP_SQL": ...
    }
}
```

### 2. 测试模式 (`PROCESS_TYPE=TEST`)

#### 2.1 执行流程
```go
case "TEST":
    jkd_import.TestIsValidFileName()
```

#### 2.2 功能说明
- **目的**: 测试文件名验证逻辑的正确性
- **测试文件**: `"049.历年真题_历年真题(推荐~new)-2024二级建造师《公路实务》真题题A卷（网络版）.xlsx"`
- **验证规则**: 
  - 必须包含关键字：章节练习、章节真题、历年真题、模拟试卷
  - 历年真题必须包含年份（2020-2024）
  - 格式：`序号.类型_描述-年份科目名称.xlsx`

#### 2.3 输出结果
- 控制台输出验证结果
- 无文件生成

### 3. 题目下载模式 (`PROCESS_TYPE=DOWNLOAD_JKD`)

#### 3.1 执行流程
```go
case "DOWNLOAD_JKD":
    jkd_dwld.QuestionDownloadAndUpZip(path.Join(dirPath, "sourceData"), subjectConfigsFromCSV)
```

#### 3.2 详细执行路径

**步骤1: 主下载流程 (`QuestionDownloadAndUpZip`)**
```go
func QuestionDownloadAndUpZip(downloadDir string, subjectConfigs []types.SubjectConfig) {
    // 1. 下载所有科目题目
    QuestionDownload(downloadDir, subjectConfigs)
    
    // 2. 解压所有下载的文件
    utils.UnzipFiles(downloadDir)
    
    // 3. 删除所有压缩文件
    utils.RemoveZipFiles(downloadDir)
}
```

**步骤2: 题目下载 (`QuestionDownload`)**
```go
func QuestionDownload(downloadDir string, subjectConfigs []types.SubjectConfig) {
    // 1. 创建/清理下载目录
    utils.DelAndCreateDirectory(downloadDir)
    
    // 2. 处理所有科目
    exportSubjects := ProcessSubjects(downloadDir, subjectConfigs)
    
    // 3. 导出更新后的科目配置
    utils.ExportSubjectConfigsToCSV(exportSubjects, "subject_configs_with_update_name.csv")
}
```

**步骤3: 科目处理 (`ProcessSubjects`)**
- 遍历科目配置文件中的每个科目
- 通过SOAP API获取题库更新信息
- 并发下载题目文件
- 更新科目配置信息

#### 3.3 目录结构生成
```
export_data/
├── sourceData/                    # 下载的原始数据
│   ├── 一级建造师/               # 按证书分类
│   │   ├── 建设工程经济/        # 按科目分类
│   │   │   ├── 001.章节练习_建设工程经济章节练习.xlsx
│   │   │   ├── 002.历年真题_建设工程经济历年真题-2024.xlsx
│   │   │   └── ...
│   │   └── 建设工程项目管理/
│   └── 二级建造师/
└── subject_configs_with_update_name.csv  # 更新后的科目配置
```

#### 3.4 配置要求
**环境变量**:
```env
PROCESS_TYPE=DOWNLOAD_JKD
SUBJECT_CONFIG_FILENAME=subject_configs_20241110.csv
EXPORT_DATA_BATH_PATH=/path/to/export
JKD_AUTH_USER_ID=your_user_id
JKD_AUTH_PASSWORD=your_password
JKD_DOWNLOAD_PROCESS_MODE=concurrent
```

**科目配置文件格式** (`subject_configs_*.csv`):
```csv
JKDCertificateId,JKDCertificateName,JKDSubjectId,JKDSubjectName,JKDUpdateName,SubDir,CertificateCode,SubjectCode
1,一级建造师,101,建设工程经济,建设工程经济,建设工程经济,YJ,JSGCJJ
```

### 4. 题库导入模式 (`PROCESS_TYPE=IMPORT_MGT`)

#### 4.1 执行流程
```go
case "IMPORT_MGT":
    jkd_import.QuestionsSetImport(dirPath, subjectConfigsFromCSV)
```

#### 4.2 详细执行路径

**步骤1: 主导入流程 (`QuestionsSetImport`)**
```go
func QuestionsSetImport(baseDir string, subjectConfigs []types.SubjectConfig) {
    for _, subjectConfig := range subjectConfigs {
        processConfig(baseDir, subjectConfig)
    }
}
```

**步骤2: 配置处理 (`processConfig`)**
- 验证科目代码不为空
- 检查证书是否在允许列表中（当前仅支持"一级建造师"）
- 检查科目是否在允许列表中（当前仅支持"YJ-JSGCJJ"）

**步骤3: 科目目录处理 (`processSubjectDirectory`)**
```go
func processSubjectDirectory(baseDir string, subjectConfig types.SubjectConfig) {
    dirPath := filepath.Join(baseDir, "excel_1217", subjectConfig.JKDCertificateName)
    subjectDirs := findSubjectDir(dirPath, subjectConfig.JKDSubjectName)
    
    for _, subjectDir := range subjectDirs {
        processExcelFilesInDirectory(baseDir, subjectDir, subjectConfig)
    }
}
```

**步骤4: Excel文件处理 (`processExcelFilesInDirectory`)**
- 遍历目录中的所有 `.xlsx` 文件
- 验证文件名格式
- 不符合要求的文件移动到删除目录
- 符合要求的文件上传到管理系统

**步骤5: 文件上传 (`uploadFile`)**
```go
func uploadFile(baseDir string, filePath string, subjectConfig types.SubjectConfig) {
    // 1. 构建multipart表单数据
    // 2. 添加requestVO字段（包含subjectCode）
    // 3. 添加文件字段
    // 4. 发送HTTP POST请求到导入API
    // 5. 处理响应结果
}
```

#### 4.3 文件名验证规则
**允许的关键字**:
- 章节练习
- 章节真题
- 历年真题（必须包含2020-2024年份）
- 模拟试卷

**文件名格式**:
```
序号.类型_描述-年份科目名称.xlsx
示例: 049.历年真题_历年真题(推荐~new)-2024二级建造师《公路实务》真题题A卷（网络版）.xlsx
```

#### 4.4 目录结构处理
```
export_data/
├── excel_1217/                    # 源文件目录
│   ├── 一级建造师/
│   │   └── 建设工程经济/
│   │       ├── 001.章节练习_建设工程经济章节练习.xlsx
│   │       └── 002.历年真题_建设工程经济历年真题-2024.xlsx
├── importDelete/                  # 删除的文件
│   ├── YJ/
│   │   └── JSGCJJ/
│   │       └── 无效文件名.xlsx
└── importSuccess/                 # 上传成功的文件记录
```

#### 4.5 配置要求
**环境变量**:
```env
PROCESS_TYPE=IMPORT_MGT
SUBJECT_CONFIG_FILENAME=subject_configs_20241110.csv
EXPORT_DATA_BATH_PATH=/path/to/export
AI_EXAM_TOKEN=your_api_token
```

**API配置**:
- 导入API地址: `http://localhost:8081/mgt/question/import-create-set`
- 请求方式: `POST`
- 内容类型: `multipart/form-data`

### 5. 图片处理模式 (`PROCESS_TYPE=QUESTION_IMAGES_HANDLE`)

#### 5.1 执行流程
```go
case "QUESTION_IMAGES_HANDLE":
    jkd_image.QuestionImageHandle(dirPath)
```

#### 5.2 详细执行路径

**步骤1: 主处理流程 (`QuestionImageHandle`)**
```go
func QuestionImageHandle(baseDir string) {
    // 1. 创建临时目录和目标目录
    tempDir := path.Join(baseDir, "tempOcr")
    destinationDir := path.Join(baseDir, "questionimagesWithJKD")
    
    // 2. 检查Tesseract OCR工具
    if !checkTesseract() {
        return
    }
    
    // 3. 统计文件总数
    stats := &ProcessStats{StartTime: startTime}
    
    // 4. 创建并发处理池
    tasks := make(chan ImageTask, maxWorkers)
    results := make(chan Result, maxWorkers)
    
    // 5. 启动worker协程
    // 6. 发送处理任务
    // 7. 收集处理结果
    // 8. 移动匹配的文件
}
```

**步骤2: 并发处理 (`worker`)**
```go
func worker(tasks <-chan ImageTask, wg *sync.WaitGroup, results chan<- Result, stats *ProcessStats) {
    for task := range tasks {
        // 1. 使用Tesseract OCR识别图片文字
        // 2. 搜索关键词：金考典、jkd、jinkaodian
        // 3. 返回匹配结果
        // 4. 更新处理统计
    }
}
```

#### 5.3 支持的图片格式
- `.jpg` / `.jpeg`
- `.png`
- `.bmp`
- `.tiff`

#### 5.4 关键词配置
```go
var keywords = []string{"金考典", "jkd", "jinkaodian"}
```

#### 5.5 目录结构处理
```
export_data/
├── questionimages/                # 源图片目录
│   ├── 图片1.jpg
│   ├── 图片2.png
│   └── ...
├── questionimagesWithJKD/         # 包含关键词的图片
│   ├── 图片1.jpg
│   └── ...
└── tempOcr/                       # 临时OCR处理目录（自动清理）
```

#### 5.6 配置要求
**环境变量**:
```env
PROCESS_TYPE=QUESTION_IMAGES_HANDLE
EXPORT_DATA_BATH_PATH=/path/to/export
```

**系统要求**:
- Tesseract OCR 工具
- 安装命令:
  - Ubuntu/Debian: `sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim`
  - MacOS: `brew install tesseract tesseract-lang`

**性能配置**:
- 最大并发数: 50个worker
- 实际worker数: `runtime.NumCPU() * 2`

### 6. 章节结构生成模式 (`PROCESS_TYPE=GENERATE_CHAP_SQL`)

#### 6.1 执行流程
```go
case "GENERATE_CHAP_SQL":
    jkd_chap_tree.ReadChapTreeCSVGenerateSQL(path.Join(dirPath, "chapterTree"))
```

#### 6.2 详细执行路径

**步骤1: 主生成流程 (`ReadChapTreeCSVGenerateSQL`)**
```go
func ReadChapTreeCSVGenerateSQL(bathDir string) {
    // 1. 读取目录下的所有CSV文件
    dirEntries, err := os.ReadDir(bathDir)
    
    // 2. 遍历CSV文件
    for _, entry := range dirEntries {
        if strings.HasSuffix(strings.ToLower(entry.Name()), ".csv") {
            // 3. 处理每个CSV文件
            file, err := os.Open(path.Join(bathDir, entry.Name()))
            readCSVGenerateSQL(file)
            file.Close()
        }
    }
}
```

**步骤2: CSV读取与SQL生成 (`readCSVGenerateSQL`)**
```go
func readCSVGenerateSQL(file *os.File) error {
    // 1. 读取CSV数据
    data, err := readChapTreeCSV(file)
    
    // 2. 生成SQL语句
    sql, err := generateSQL(data)
    
    // 3. 写入SQL文件
    fileName := strings.TrimSuffix(filepath.Base(file.Name()), ".csv")
    outputDir := "./chapTreeSQL"
    os.WriteFile(filepath.Join(outputDir, fileName+".sql"), []byte(sql), 0644)
}
```

**步骤3: CSV数据解析 (`readChapTreeCSV`)**
- 解析篇-章-节三级结构
- 过滤不需要的知识点（试看、综合练习等）
- 构建KnowledgeNode数据结构

**步骤4: SQL生成 (`generateSQL`)**
```go
func generateSQL(data []KnowledgeNode) (string, error) {
    // 1. 生成篇级INSERT语句
    // 2. 生成章级INSERT语句
    // 3. 生成节级INSERT语句
    // 4. 组合完整的SQL脚本
}
```

#### 6.3 编码规则
**篇编码**: `科目代码_篇序号`
**章编码**: `科目代码_章序号`
**节编码**: `章编码_节序号`

**示例**:
- 篇: `JSGCJJ_1` (建设工程经济第1篇)
- 章: `JSGCJJ_1` (建设工程经济第1章)
- 节: `JSGCJJ_1_1` (第1章第1节)

#### 6.4 过滤规则
**过滤关键词**:
```go
var filterKeywords = []string{
    "试看", "综合练习", "教材变动", "强化练习",
    "案例精选", "案例备选", "附录", "案例分析", "案例练习"
}
```

#### 6.5 目录结构处理
```
export_data/
├── chapterTree/                   # 章节树CSV文件目录
│   ├── 建设工程经济.csv
│   ├── 建设工程项目管理.csv
│   └── ...
└── chapTreeSQL/                   # 生成的SQL文件
    ├── 建设工程经济.sql
    ├── 建设工程项目管理.sql
    └── ...
```

#### 6.6 配置要求
**环境变量**:
```env
PROCESS_TYPE=GENERATE_CHAP_SQL
EXPORT_DATA_BATH_PATH=/path/to/export
```

**CSV文件格式**:
```csv
证书代码,科目代码,科目名称,篇名称,章名称,节名称
YJ,JSGCJJ,建设工程经济,第一篇 工程经济,第一章 资金时间价值的计算及应用,第一节 利息的计算
```

## 数据模型

### SubjectConfig (科目配置)
```go
type SubjectConfig struct {
    JKDCertificateId   string  // JKD 证书 ID
    JKDCertificateName string  // JKD 证书名称
    JKDSubjectId       string  // JKD 科目 ID
    JKDSubjectName     string  // JKD 科目名称
    JKDUpdateName      string  // JKD 更新名称
    SubDir             string  // 子目录名称
    CertificateCode    string  // 证书代码
    SubjectCode        string  // 科目代码
}
```

### KnowledgeNode (知识节点)
```go
type KnowledgeNode struct {
    CertificateCode string  // 证书代码
    SubjectCode     string  // 科目代码
    SubjectName     string  // 科目名称
    ArticleName     string  // 篇名称
    ChapterName     string  // 章名称
    SectionName     string  // 节名称
}
```

## 环境设置与运行指南

### 1. 环境要求
- Go 1.23.2+
- Python 3.x (辅助脚本)
- Tesseract OCR (图片文字识别)

### 2. 依赖安装
```bash
# 安装Go依赖
go mod download

# 安装Tesseract OCR
# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# MacOS
brew install tesseract tesseract-lang
```

### 3. 配置文件设置
**创建 `.env` 文件**:
```env
PROCESS_TYPE=DOWNLOAD_JKD
SUBJECT_CONFIG_FILENAME=subject_configs_20241110.csv
EXPORT_DATA_BATH_PATH=/path/to/export
```

**创建 `.env.jkd` 文件**:
```env
JKD_AUTH_USER_ID=your_user_id
JKD_AUTH_PASSWORD=your_password
JKD_DOWNLOAD_PROCESS_MODE=concurrent
AI_EXAM_TOKEN=your_token
```

### 4. 运行命令
```bash
# 编译项目
go build -o jkd-downloader ./cmd

# 测试文件名验证
PROCESS_TYPE=TEST ./jkd-downloader

# 下载题目
PROCESS_TYPE=DOWNLOAD_JKD ./jkd-downloader

# 导入题库
PROCESS_TYPE=IMPORT_MGT ./jkd-downloader

# 处理图片
PROCESS_TYPE=QUESTION_IMAGES_HANDLE ./jkd-downloader

# 生成章节SQL
PROCESS_TYPE=GENERATE_CHAP_SQL ./jkd-downloader
```

## 开发状态跟踪

| 模块/功能 | 状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|-----------|------|--------|--------------|--------------|------------|
| 主程序入口 | 已完成 | AI | 2024-11-10 | 2024-11-10 | 支持5种执行模式 |
| 题目下载模块 | 已完成 | AI | 2024-11-10 | 2024-11-10 | 支持并发下载和解压 |
| 题库导入模块 | 已完成 | AI | 2024-11-10 | 2024-11-10 | 支持文件名验证和API上传 |
| 图片处理模块 | 已完成 | AI | 2024-11-10 | 2024-11-10 | 支持OCR识别和并发处理 |
| 章节SQL生成模块 | 已完成 | AI | 2024-11-10 | 2024-11-10 | 支持三级结构生成 |

## 代码检查与问题记录

### 已解决的问题
1. **文件名验证逻辑优化** - 使用正则表达式提高验证准确性
2. **并发处理优化** - 限制最大并发数避免系统资源耗尽
3. **错误处理完善** - 添加详细的错误日志和异常处理

### 待优化项目
1. **配置管理** - 考虑使用配置文件替代环境变量
2. **日志系统** - 集成结构化日志框架
3. **单元测试** - 为各模块添加完整的测试用例

## 技术实现细节

### 1. 并发下载实现
- 使用Go协程实现并发下载
- 通过channel控制并发数量
- 支持断点续传和错误重试

### 2. OCR图片识别
- 集成Tesseract OCR引擎
- 支持中文简体识别
- 并发处理提高效率

### 3. 文件上传机制
- 使用multipart/form-data格式
- 支持大文件上传
- 添加进度显示和错误处理

### 4. SQL生成算法
- 解析CSV三级结构
- 自动生成编码规则
- 过滤无效知识点

## 部署指南

### 1. 生产环境部署
```bash
# 1. 编译生产版本
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o jkd-downloader ./cmd

# 2. 创建部署目录
mkdir -p /opt/jkd-downloader

# 3. 复制可执行文件和配置文件
cp jkd-downloader /opt/jkd-downloader/
cp .env* /opt/jkd-downloader/
cp subject_configs_*.csv /opt/jkd-downloader/

# 4. 设置权限
chmod +x /opt/jkd-downloader/jkd-downloader
```

### 2. Docker部署
```dockerfile
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o jkd-downloader ./cmd

FROM alpine:latest
RUN apk --no-cache add tesseract tesseract-data-chi-sim
WORKDIR /app
COPY --from=builder /app/jkd-downloader .
COPY .env* ./
COPY subject_configs_*.csv ./
CMD ["./jkd-downloader"]
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。 