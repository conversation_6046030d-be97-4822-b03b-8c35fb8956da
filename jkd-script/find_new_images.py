#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# find_new_images.py
# 功能：比较两个目录，找出新增的图片文件并复制到目标目录
# 使用场景：用于查找和复制新增的图片文件

import os
import shutil

def process_images():
    source_dir = "D:\work-ai\AI-Project\jkd\work\export_data\questionimages_20241217"
    compare_dir = "D:\work-ai\AI-Project\jkd\work\export_data\questionimages_20241114"
    target_dir = "D:\work-ai\AI-Project\jkd\work\export_data\questionimages_add"
    
    # Counter for new images
    new_images_count = 0
    
    # Walk through all files in source directory
    for root, dirs, files in os.walk(source_dir):
        # Get relative path from source directory
        rel_path = os.path.relpath(root, source_dir)
        
        for file in files:
            # Construct paths
            source_file = os.path.join(root, file)
            compare_file = os.path.join(compare_dir, rel_path, file)
            target_file = os.path.join(target_dir, rel_path, file)
            
            # Check if file exists in compare directory
            if not os.path.exists(compare_file):
                print(f"File not found in compare dir: {compare_file}")
                # Create target directory if it doesn't exist
                target_file_dir = os.path.dirname(target_file)
                if not os.path.exists(target_file_dir):
                    os.makedirs(target_file_dir)
                # Copy file to target directory
                shutil.copy2(source_file, target_file)
                print(f"Copied to: {target_file}")
                new_images_count += 1
    
    return new_images_count

if __name__ == "__main__":
    try:
        new_images = process_images()
        print(f"\nProcess completed successfully!")
        print(f"Total new images copied: {new_images}")
    except Exception as e:
        print(f"An error occurred: {str(e)}")
