#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# count_images.py
# 功能：递归统计指定目录下的图片文件数量
# 支持的图片格式：jpg, jpeg, png, gif, bmp, webp

import os
from pathlib import Path

def count_image_files(directory):
    # 定义图片文件的扩展名
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    
    # 初始化计数器
    count = 0
    
    # 使用 Path 对象递归遍历目录
    for file_path in Path(directory).rglob('*'):
        # 检查文件扩展名（转换为小写进行比较）
        if file_path.suffix.lower() in image_extensions:
            count += 1
            # 打印找到的图片文件路径
            print(f"找到图片: {file_path}")
    
    return count

# 指定要搜索的目录
target_dir = 'D:\work-ai\AI-Project\jkd\work\export_data\questionimages_sensitive'

# 确保目录存在
if os.path.exists(target_dir):
    total_images = count_image_files(target_dir)
    print(f"\n目录中共找到 {total_images} 个图片文件")
else:
    print(f"目录 {target_dir} 不存在！")