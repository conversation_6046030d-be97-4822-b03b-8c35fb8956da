#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# image_content_filter.py
# 功能：根据图片中的文字内容将图片分类到不同目录

import os
from pathlib import Path
import shutil
from PIL import Image
import pytesseract
import time

def process_images(source_dir, sensitive_dir, clean_dir):
    # 定义敏感词列表
    sensitive_words = ["jkd", "jinkaodian", "金考典", "建匠教育"]
    
    # 定义图片文件的扩展名
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    
    # 创建目标目录（如果不存在）
    os.makedirs(sensitive_dir, exist_ok=True)
    os.makedirs(clean_dir, exist_ok=True)
    
    # 统计变量
    total_files = 0
    sensitive_count = 0
    clean_count = 0
    error_count = 0
    
    # 记录开始时间
    start_time = time.time()
    
    # 遍历所有图片文件
    for file_path in Path(source_dir).rglob('*'):
        if file_path.suffix.lower() in image_extensions:
            total_files += 1
            try:
                print(f"\n处理第 {total_files} 个文件: {file_path}")
                
                # 使用 OCR 识别图片中的文字
                img = Image.open(file_path)
                text = pytesseract.image_to_string(img, lang='chi_sim+eng')
                
                # 检查是否包含敏感词
                contains_sensitive = any(word.lower() in text.lower() for word in sensitive_words)
                
                # 确定目标目录
                target_dir = sensitive_dir if contains_sensitive else clean_dir
                
                # 构建目标文件路径，保持原始的目录结构
                relative_path = file_path.relative_to(source_dir)
                target_path = Path(target_dir) / relative_path
                
                # 创建必要的子目录
                os.makedirs(target_path.parent, exist_ok=True)
                
                # 复制文件
                shutil.copy2(file_path, target_path)
                
                # 更新计数器
                if contains_sensitive:
                    sensitive_count += 1
                else:
                    clean_count += 1
                
                # 打印处理信息
                status = "敏感" if contains_sensitive else "正常"
                print(f"状态: {status}")
                
            except Exception as e:
                error_count += 1
                print(f"处理出错: {str(e)}")
                continue
    
    # 计算总耗时
    total_time = time.time() - start_time
    
    # 打印统计信息
    print("\n" + "="*50)
    print("处理完成！统计信息：")
    print(f"总文件数: {total_files}")
    print(f"包含敏感词的文件数: {sensitive_count}")
    print(f"正常文件数: {clean_count}")
    print(f"处理出错的文件数: {error_count}")
    print(f"总耗时: {total_time:.2f} 秒")
    print("="*50)

def main():
    # 定义目录路径
    source_directory = "D:\work-ai\AI-Project\jkd\work\export_data\questionimages_add"
    sensitive_directory = "D:\work-ai\AI-Project\jkd\work\export_data\questionimages_sensitive"
    clean_directory = "D:\work-ai\AI-Project\jkd\work\export_data\questionimages_clean"

    # 检查源目录是否存在
    if not os.path.exists(source_directory):
        print(f"错误：源目录 {source_directory} 不存在！")
        return

    print("开始处理图片...")
    process_images(source_directory, sensitive_directory, clean_directory)

if __name__ == "__main__":
    main()