import Foundation
import Vision
import AppKit

// 固定的图片目录和搜索文本
let imageDirectory = "D:\\work-ai\\AI-Project\\jkd\\work\\export_data\\questionimages"
let searchText = "金考典"

// 获取开始时间
let startTimeOverall = Date()

// 预加载所有图像路径（递归遍历子目录）
let fileManager = FileManager.default
let imageDirectoryURL = URL(fileURLWithPath: imageDirectory)

let keys = [URLResourceKey.isRegularFileKey]
guard let enumerator = fileManager.enumerator(at: imageDirectoryURL, includingPropertiesForKeys: keys, options: [.skipsHiddenFiles, .skipsPackageDescendants]) else {
    print("无法创建目录枚举器：\(imageDirectory)")
    exit(1)
}

var urls = [URL]()
for case let fileURL as URL in enumerator {
    if let resourceValues = try? fileURL.resourceValues(forKeys: Set(keys)),
       resourceValues.isRegularFile == true {
        // 只处理图片文件，可以根据需要过滤文件类型
        let allowedExtensions = ["jpg", "jpeg", "png", "bmp", "tiff"]
        if allowedExtensions.contains(fileURL.pathExtension.lowercased()) {
            urls.append(fileURL)
        }
    }
}

let totalFiles = urls.count

if totalFiles == 0 {
    print("目录中未找到图片文件：\(imageDirectory)")
    exit(0)
}

let concurrentQueue = DispatchQueue(label: "com.ocr.concurrentQueue", attributes: .concurrent)
let semaphore = DispatchSemaphore(value: ProcessInfo.processInfo.processorCount)


// 添加变量来跟踪已处理的文件数量
var processedFiles = 0
let processedFilesLock = NSLock()

// 添加变量来跟踪时间
var lastCheckpointTime = Date()
let timeLock = NSLock()

let group = DispatchGroup()

for url in urls {
    semaphore.wait()
    group.enter()
    concurrentQueue.async {
        defer {
            // 更新已处理文件计数，并计算时间和进度
            processedFilesLock.lock()
            processedFiles += 1
            let currentProcessedFiles = processedFiles
            processedFilesLock.unlock()

            if currentProcessedFiles % 100 == 0 || currentProcessedFiles == totalFiles {
                timeLock.lock()
                let currentTime = Date()
                let elapsedTime = currentTime.timeIntervalSince(startTimeOverall)
                let averageTimePerImage = elapsedTime / Double(currentProcessedFiles)
                let estimatedTotalTime = averageTimePerImage * Double(totalFiles)
                let remainingTime = estimatedTotalTime - elapsedTime
                print(String(format: "已处理 %d/%d 个文件。耗时：%.2f 秒。预计剩余时间：%.2f 秒", currentProcessedFiles, totalFiles, elapsedTime, remainingTime))
                lastCheckpointTime = currentTime
                timeLock.unlock()
            }

            semaphore.signal()
            group.leave()
        }

        var containsSearchText = false
        autoreleasepool {
            containsSearchText = processImage(at: url)
        }

        // 如果不包含搜索文本，删除文件
        if !containsSearchText {
            do {
                try fileManager.removeItem(at: url)
            } catch {
                print("无法删除文件 \(url.path)：\(error)")
            }
        }

        // 注意：此处不再需要更新计数器和打印进度
    }
}

group.wait()

print("处理完成。")

func processImage(at url: URL) -> Bool {
    guard let imageData = try? Data(contentsOf: url) else { return false }
    guard let image = NSImage(data: imageData) else { return false }
    guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else { return false }

    var containsSearchText = false

    let request = VNRecognizeTextRequest { (request, error) in
        if let error = error {
            print("识别文本出错 \(url.lastPathComponent)：\(error)")
            return
        }
        guard let observations = request.results as? [VNRecognizedTextObservation] else { return }
        
        // 打印文件名
        print("\n处理文件：\(url.lastPathComponent)")
        print("识别到的文本：")
        
        // 收集所有识别到的文本
        var allText = ""
        for observation in observations {
            if let topCandidate = observation.topCandidates(1).first {
                let recognizedText = topCandidate.string
                allText += recognizedText + " "
                
                // 打印每个识别到的文本片段
                print("- \(recognizedText)")
                
                // 如果找到目标文本，标记出来
                if recognizedText.contains(searchText) {
                    containsSearchText = true
                    print("✅ 找到目标文本：\(searchText)")
                }
            }
        }
        
        // 打印Unicode编码（帮助调试空格问题）
        print("Unicode编码：")
        for scalar in allText.unicodeScalars {
            print(String(format: "U+%04X", scalar.value), terminator: " ")
        }
        print("\n" + String(repeating: "-", count: 50))
    }

    // 设置请求的属性
    request.recognitionLanguages = ["zh-Hans"]
    request.usesLanguageCorrection = true
    request.recognitionLevel = .accurate

    // 创建请求处理器
    let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
    do {
        try handler.perform([request])
    } catch {
        print("执行文本识别失败 \(url.lastPathComponent)：\(error)")
    }

    return containsSearchText
}