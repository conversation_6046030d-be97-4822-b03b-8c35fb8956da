// image_content_classifier.swift
// 功能：基于图片内容中的文字识别，将图片分类到不同目录
// 创建日期：2024-12-29

import Foundation
import Vision
import AppKit

// 配置目录和搜索文本
let sourceDirectory = "D:\\work-ai\\AI-Project\\jkd\\work\\export_data\\questionimages"
let sensitiveDirectory = "D:\\work-ai\\AI-Project\\jkd\\work\\export_data\\questionimages_sensitive"
let cleanDirectory = "D:\\work-ai\\AI-Project\\jkd\\work\\export_data\\questionimages_clean"
// let sensitiveWords = ["jkd", "jinkaodian", "金考典", "建匠教育"]
// 修改敏感词列表，添加不同空格格式的版本
let sensitiveWords = [
    "金 考 典",    // 半角空格
    "金　考　典",  // 全角空格
]

// 创建目标目录
let fileManager = FileManager.default
try? fileManager.createDirectory(atPath: sensitiveDirectory, withIntermediateDirectories: true)
try? fileManager.createDirectory(atPath: cleanDirectory, withIntermediateDirectories: true)

// 获取开始时间
let startTimeOverall = Date()

// 预加载所有图像路径
let sourceDirectoryURL = URL(fileURLWithPath: sourceDirectory)
let keys = [URLResourceKey.isRegularFileKey]
guard let enumerator = fileManager.enumerator(at: sourceDirectoryURL, includingPropertiesForKeys: keys, options: [.skipsHiddenFiles, .skipsPackageDescendants]) else {
    print("无法创建目录枚举器：\(sourceDirectory)")
    exit(1)
}

var urls = [URL]()
for case let fileURL as URL in enumerator {
    if let resourceValues = try? fileURL.resourceValues(forKeys: Set(keys)),
       resourceValues.isRegularFile == true {
        let allowedExtensions = ["jpg", "jpeg", "png", "bmp", "tiff"]
        if allowedExtensions.contains(fileURL.pathExtension.lowercased()) {
            urls.append(fileURL)
        }
    }
}

let totalFiles = urls.count
if totalFiles == 0 {
    print("目录中未找到图片文件：\(sourceDirectory)")
    exit(0)
}

let concurrentQueue = DispatchQueue(label: "com.ocr.concurrentQueue", attributes: .concurrent)
let semaphore = DispatchSemaphore(value: ProcessInfo.processInfo.processorCount)

var processedFiles = 0
let processedFilesLock = NSLock()
var lastCheckpointTime = Date()
let timeLock = NSLock()

let group = DispatchGroup()

// 添加错误计数器
var errorCount = 0
let errorCountLock = NSLock()

for url in urls {
    semaphore.wait()
    group.enter()
    concurrentQueue.async {
        defer {
            processedFilesLock.lock()
            processedFiles += 1
            let currentProcessedFiles = processedFiles
            processedFilesLock.unlock()

            if currentProcessedFiles % 100 == 0 || currentProcessedFiles == totalFiles {
                timeLock.lock()
                let currentTime = Date()
                let elapsedTime = currentTime.timeIntervalSince(startTimeOverall)
                let averageTimePerImage = elapsedTime / Double(currentProcessedFiles)
                let estimatedTotalTime = averageTimePerImage * Double(totalFiles)
                let remainingTime = estimatedTotalTime - elapsedTime
                print(String(format: "已处理 %d/%d 个文件。耗时：%.2f 秒。预计剩余时间：%.2f 秒", currentProcessedFiles, totalFiles, elapsedTime, remainingTime))
                lastCheckpointTime = currentTime
                timeLock.unlock()
            }

            semaphore.signal()
            group.leave()
        }

        var containsSensitiveText = false
        autoreleasepool {
            containsSensitiveText = processImage(at: url)
        }

        // 构建目标路径
        let relativePath = url.path.replacingOccurrences(of: sourceDirectory, with: "")
        let targetDirectory = containsSensitiveText ? sensitiveDirectory : cleanDirectory
        let targetPath = targetDirectory + relativePath
        
        // 创建必要的子目录
        try? fileManager.createDirectory(atPath: (targetPath as NSString).deletingLastPathComponent, 
                                       withIntermediateDirectories: true)
        
        // 复制文件
        do {
            try fileManager.copyItem(atPath: url.path, toPath: targetPath)
        } catch {
            errorCountLock.lock()
            errorCount += 1
            errorCountLock.unlock()
            print("复制文件失败 \(url.path)：\(error)")
        }
    }
}

group.wait()

// 统计函数
func countImages(in directory: String) -> Int {
    guard let enumerator = FileManager.default.enumerator(
        at: URL(fileURLWithPath: directory),
        includingPropertiesForKeys: [.isRegularFileKey],
        options: [.skipsHiddenFiles, .skipsPackageDescendants]
    ) else { return 0 }
    
    var count = 0
    for case let fileURL as URL in enumerator {
        if let resourceValues = try? fileURL.resourceValues(forKeys: Set([.isRegularFileKey])),
           resourceValues.isRegularFile == true {
            let ext = fileURL.pathExtension.lowercased()
            if ["jpg", "jpeg", "png", "bmp", "tiff"].contains(ext) {
                count += 1
            }
        }
    }
    return count
}

// 计算总耗时
let totalTime = Date().timeIntervalSince(startTimeOverall)

// 获取统计结果
let sensitiveCount = countImages(in: sensitiveDirectory)
let cleanCount = countImages(in: cleanDirectory)

// 打印统计信息
print("\n处理完成！统计信息：")
print(String(repeating: "=", count: 50))
print("总耗时：\(String(format: "%.2f", totalTime)) 秒")
print("总文件数：\(totalFiles) 个")
print("包含敏感词的图片：\(sensitiveCount) 个")
print("不包含敏感词的图片：\(cleanCount) 个")
print("处理失败的文件数：\(errorCount) 个")
print("处理速度：\(String(format: "%.2f", Double(totalFiles) / totalTime)) 张/秒")
print(String(repeating: "=", count: 50))

// OCR处理函数
func processImage(at url: URL) -> Bool {
    guard let imageData = try? Data(contentsOf: url) else { return false }
    guard let image = NSImage(data: imageData) else { return false }
    guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else { return false }

    var containsSensitiveText = false

    let request = VNRecognizeTextRequest { (request, error) in
        if let error = error {
            print("识别文本出错 \(url.lastPathComponent)：\(error)")
            return
        }
        guard let observations = request.results as? [VNRecognizedTextObservation] else { return }
        for observation in observations {
            if let topCandidate = observation.topCandidates(1).first {
                let recognizedText = topCandidate.string.lowercased()
                // 添加调试输出，显示识别到的文本和空格的 Unicode 编码
                print("识别到的文本: \(recognizedText)")
                
                // 检查是否包含任何敏感词
                for word in sensitiveWords {
                    if recognizedText.contains(word.lowercased()) {
                        containsSensitiveText = true
                        break
                    }
                }
                if containsSensitiveText {
                    break
                }
            }
        }
    }

    request.recognitionLanguages = ["zh-Hans"]
    request.usesLanguageCorrection = true
    request.recognitionLevel = .accurate

    let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
    do {
        try handler.perform([request])
    } catch {
        print("执行文本识别失败 \(url.lastPathComponent)：\(error)")
    }

    return containsSensitiveText
}